// EmulatorJS Gambatte Core Loader
var Module = typeof Module !== 'undefined' ? Module : {};

Module['preRun'] = Module['preRun'] || [];
Module['postRun'] = Module['postRun'] || [];

Module['preRun'].push(function() {
    console.log('Gambatte core initializing...');
});

Module['postRun'].push(function() {
    console.log('Gambatte core ready');
});

// Core configuration
Module['locateFile'] = function(path, prefix) {
    if (path.endsWith('.wasm')) {
        return prefix + 'cores/gambatte-wasm.wasm';
    }
    if (path.endsWith('.data')) {
        return prefix + 'cores/gambatte-wasm.data';
    }
    return prefix + path;
};

// Export for EmulatorJS
if (typeof window !== 'undefined') {
    window.Module = Module;
}
