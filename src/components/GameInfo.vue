<template>
  <div class="game-info">
    <div class="info-card">
      <div class="game-header">
        <div class="game-icon">🎮</div>
        <div class="game-details">
          <h2>{{ game.name }}</h2>
          <p class="game-size">{{ formatFileSize(game.size) }}</p>
          <p class="game-ext">.{{ game.extension.toUpperCase() }} 文件</p>
        </div>
        <button @click="$emit('back')" class="btn-back">← 返回</button>
      </div>

      <div v-if="core" class="core-info">
        <h3>已选择的核心：</h3>
        <div class="core-card">
          <h4>{{ core.name }}</h4>
          <p>{{ core.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GameFile, EmulatorCore } from "../types/emulator";
import { formatFileSize } from "../utils/fileHandler";

defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
}>();

defineEmits<{
  back: [];
}>();
</script>

<style scoped>
.game-info {
  width: 100%;
  margin-bottom: 2rem;
}

.info-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.game-icon {
  font-size: 3rem;
}

.game-details {
  flex: 1;
  color: white;
}

.game-details h2 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.game-size {
  opacity: 0.8;
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

.game-ext {
  opacity: 0.7;
  font-size: 0.9rem;
  text-transform: uppercase;
  font-weight: 600;
}

.btn-back {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.btn-back:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.core-info {
  color: white;
}

.core-info h3 {
  margin-bottom: 1rem;
  font-weight: 600;
  opacity: 0.9;
}

.core-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.core-card h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.core-card p {
  opacity: 0.8;
  line-height: 1.5;
}
</style>
