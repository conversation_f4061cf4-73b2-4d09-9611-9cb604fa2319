<template>
  <div class="file-uploader">
    <div
      class="upload-area"
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
    >
      <div class="upload-content">
        <div class="upload-icon">📁</div>
        <h3>将游戏文件拖拽到这里</h3>
        <p>或点击选择文件</p>
        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".gb,.gbc,.gba,.nes,.smc,.sfc,.md,.gen,.sms,.a26,.nds,.n64,.v64,.z64,.bin,.cue,.zip"
          @change="handleFileSelect"
          class="file-input"
        />
        <button @click="$refs.fileInput.click()" class="btn-primary">
          选择文件
        </button>
      </div>
    </div>

    <div v-if="supportedFormats.length > 0" class="supported-formats">
      <h4>支持的格式：</h4>
      <div class="format-list">
        <span
          v-for="format in supportedFormats"
          :key="format"
          class="format-tag"
        >
          .{{ format }}
        </span>
      </div>
    </div>

    <div v-if="isProcessing" class="processing">
      <div class="loading-spinner"></div>
      <p>正在处理文件...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  handleFileUpload,
  getAllSupportedExtensions,
} from "../utils/fileHandler";
import type { GameFile } from "../types/emulator";

const emit = defineEmits<{
  "files-selected": [games: GameFile[]];
}>();

const fileInput = ref<HTMLInputElement>();
const isProcessing = ref(false);
const supportedFormats = ref<string[]>([]);

onMounted(() => {
  supportedFormats.value = getAllSupportedExtensions();
});

async function processFiles(files: FileList) {
  if (files.length === 0) return;

  isProcessing.value = true;
  try {
    const allGames: GameFile[] = [];

    for (const file of Array.from(files)) {
      try {
        const games = await handleFileUpload(file);
        allGames.push(...games);
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
      }
    }

    if (allGames.length > 0) {
      emit("files-selected", allGames);
    } else {
      alert("未找到支持的游戏文件！");
    }
  } catch (error) {
    console.error("Error processing files:", error);
    alert("处理文件时出错，请重试。");
  } finally {
    isProcessing.value = false;
  }
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    processFiles(target.files);
  }
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  if (event.dataTransfer?.files) {
    processFiles(event.dataTransfer.files);
  }
}
</script>

<style scoped>
.file-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
}

.upload-content {
  color: white;
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.upload-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.upload-content p {
  opacity: 0.8;
  margin-bottom: 2rem;
}

.file-input {
  display: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.supported-formats {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.supported-formats h4 {
  color: white;
  margin-bottom: 1rem;
  font-weight: 600;
}

.format-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.format-tag {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.processing {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  color: white;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
