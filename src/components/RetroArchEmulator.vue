<template>
  <div class="retroarch-emulator">
    <div class="emulator-container">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h3>正在加载RetroArch模拟器...</h3>
          <p>正在下载 {{ core?.name }} 核心...</p>
        </div>
      </div>

      <canvas id="canvas" class="game-canvas"></canvas>

      <div class="controls">
        <button @click="stopEmulator" class="btn-stop">⏹️ 停止游戏</button>
        <button @click="$emit('back')" class="btn-back">← 返回选择</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
  back: [];
}>();

const loading = ref(true);
let Module: any = null;

onMounted(async () => {
  if (props.core && props.game) {
    try {
      await initializeRetroArch();
    } catch (error) {
      console.error("RetroArch初始化失败:", error);
      emit("error", "RetroArch初始化失败: " + (error as Error).message);
    }
  }
});

async function initializeRetroArch() {
  try {
    loading.value = true;

    console.log("开始加载RetroArch模拟器...");

    // 根据核心类型下载对应的RetroArch核心
    const coreUrl = getRetroArchCoreUrl(props.core?.id || "gambatte");

    console.log("下载核心文件:", coreUrl);

    // 设置Module配置
    (window as any).Module = {
      canvas: document.getElementById("canvas"),
      arguments: [props.game.name],
      preRun: [],
      postRun: [],
      print: (text: string) => console.log("RetroArch:", text),
      printErr: (text: string) => console.error("RetroArch:", text),
      onRuntimeInitialized: () => {
        console.log("✅ RetroArch运行时初始化完成");
        setupGameFile();
      },
      totalDependencies: 0,
      monitorRunDependencies: (left: number) => {
        if (left === 0) {
          console.log("✅ 所有依赖加载完成，游戏即将开始");
          loading.value = false;
        }
      },
    };

    // 动态加载RetroArch核心脚本
    await loadRetroArchCore(coreUrl);
  } catch (error) {
    loading.value = false;
    throw error;
  }
}

function getRetroArchCoreUrl(coreId: string): string {
  // 使用libretro的官方构建
  const coreMap: { [key: string]: string } = {
    gambatte: "gambatte_libretro",
    mgba: "mgba_libretro",
    snes9x: "snes9x_libretro",
    fceumm: "fceumm_libretro",
    genesis_plus_gx: "genesis_plus_gx_libretro",
  };

  const coreName = coreMap[coreId] || "gambatte_libretro";
  return `https://buildbot.libretro.com/nightly/emscripten/latest/${coreName}.js`;
}

async function loadRetroArchCore(coreUrl: string) {
  return new Promise<void>((resolve, reject) => {
    const script = document.createElement("script");
    script.src = coreUrl;
    script.onload = () => {
      console.log("✅ RetroArch核心脚本加载成功");
      resolve();
    };
    script.onerror = (error) => {
      console.error("❌ RetroArch核心脚本加载失败:", error);
      reject(new Error("无法加载RetroArch核心"));
    };
    document.head.appendChild(script);
  });
}

function setupGameFile() {
  if (!Module || !props.game) return;

  try {
    console.log("设置游戏文件...");

    // 创建游戏文件
    const gameData = new Uint8Array(props.game.data);
    Module.FS.writeFile(props.game.name, gameData);

    console.log("✅ 游戏文件写入成功，大小:", gameData.length);

    // 启动游戏
    Module.callMain([props.game.name]);

    console.log("🎮 游戏启动完成！");
  } catch (error) {
    console.error("❌ 设置游戏文件失败:", error);
    emit("error", "游戏启动失败: " + (error as Error).message);
  }
}

function stopEmulator() {
  if (Module) {
    try {
      // 尝试停止RetroArch
      if (Module._main) {
        Module._exit(0);
      }
    } catch (error) {
      console.error("停止模拟器时出错:", error);
    }
  }
  emit("stop");
}

onUnmounted(() => {
  stopEmulator();
});
</script>

<style scoped>
.retroarch-emulator {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.emulator-container {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.loading-content p {
  opacity: 0.8;
}

.game-canvas {
  width: 100%;
  height: 400px;
  background: #000;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: block;
}

.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.controls button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.9rem;
}

.controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-stop {
  background: rgba(244, 67, 54, 0.2) !important;
  border-color: rgba(244, 67, 54, 0.3) !important;
}

.btn-stop:hover {
  background: rgba(244, 67, 54, 0.3) !important;
}

.btn-back {
  background: rgba(108, 117, 125, 0.2) !important;
  border-color: rgba(108, 117, 125, 0.3) !important;
}

.btn-back:hover {
  background: rgba(108, 117, 125, 0.3) !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
