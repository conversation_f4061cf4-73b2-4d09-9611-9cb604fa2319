<template>
  <div class="app">
    <header class="header">
      <h1>🎮 游戏模拟器</h1>
      <p>上传你的游戏文件，在浏览器中畅玩经典游戏！</p>
    </header>

    <main class="main">
      <div v-if="!state.currentGame" class="upload-section">
        <div class="default-game-section">
          <button @click="loadDefaultGame" class="btn-default-game">
            🎮 加载示例Game Boy游戏
          </button>
          <p class="or-divider">或者</p>
        </div>
        <FileUploader @files-selected="handleFilesSelected" />
      </div>

      <div v-else class="game-section">
        <GameInfo
          :game="state.currentGame"
          :core="state.selectedCore"
          @back="resetGame"
        />

        <CoreSelector
          v-if="!state.isPlaying"
          :game="state.currentGame"
          :selected-core="state.selectedCore"
          @core-selected="handleCoreSelected"
          @play="startGame"
        />

        <RetroArchEmulator
          v-if="state.isPlaying"
          :game="state.currentGame"
          :core="state.selectedCore"
          :loading="state.isLoading"
          @error="handleError"
          @stop="stopGame"
          @back="stopGame"
        />
      </div>

      <div v-if="state.error" class="error">
        <p>❌ {{ state.error }}</p>
        <button @click="state.error = null" class="btn-secondary">关闭</button>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import type { EmulatorState, GameFile, EmulatorCore } from "./types/emulator";
import { loadDefaultGame as loadDefault } from "./utils/gameLoader";
import FileUploader from "./components/FileUploader.vue";
import GameInfo from "./components/GameInfo.vue";
import CoreSelector from "./components/CoreSelector.vue";
import RetroArchEmulator from "./components/RetroArchEmulator.vue";

const state = reactive<EmulatorState>({
  isLoading: false,
  isPlaying: false,
  error: null,
  currentGame: null,
  selectedCore: null,
});

function handleFilesSelected(games: GameFile[]) {
  if (games.length > 0) {
    state.currentGame = games[0]; // For now, just take the first game
    state.error = null;
  }
}

function handleCoreSelected(core: EmulatorCore) {
  state.selectedCore = core;
}

function startGame() {
  if (state.currentGame && state.selectedCore) {
    state.isPlaying = true;
    state.isLoading = true;
  }
}

function stopGame() {
  state.isPlaying = false;
  state.isLoading = false;
}

function resetGame() {
  state.currentGame = null;
  state.selectedCore = null;
  state.isPlaying = false;
  state.isLoading = false;
  state.error = null;
}

function handleError(error: string) {
  state.error = error;
  state.isLoading = false;
}

async function loadDefaultGame() {
  try {
    const game = await loadDefault();
    if (game) {
      state.currentGame = game;
      state.error = null;
    } else {
      state.error = "未找到默认游戏文件";
    }
  } catch (error) {
    state.error = "加载默认游戏失败: " + (error as Error).message;
  }
}
</script>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  padding: 2rem;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.main {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.upload-section,
.game-section {
  width: 100%;
  max-width: 800px;
}

.error {
  background: rgba(255, 82, 82, 0.1);
  border: 1px solid rgba(255, 82, 82, 0.3);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  text-align: center;
  backdrop-filter: blur(10px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
}

.default-game-section {
  text-align: center;
  margin-bottom: 2rem;
}

.btn-default-game {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
  margin-bottom: 1rem;
}

.btn-default-game:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.or-divider {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  margin: 1rem 0;
  font-weight: 500;
}
</style>
