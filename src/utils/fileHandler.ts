/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:15:48
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-29 19:26:00
 * @Description: 
 * 
 */
import type { GameFile } from '../types/emulator'
import { getAllSupportedExtensions } from './cores'

// Re-export the function so it can be imported from this module
export { getAllSupportedExtensions }

export async function handleFileUpload(file: File): Promise<GameFile[]> {
  const supportedExtensions = getAllSupportedExtensions()
  const extension = getFileExtension(file.name)

  if (supportedExtensions.includes(extension)) {
    const arrayBuffer = await file.arrayBuffer()
    return [{
      name: file.name,
      extension,
      data: arrayBuffer,
      size: file.size
    }]
  } else {
    throw new Error(`不支持的文件格式: .${extension}`)
  }
}

export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
} 