/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:22:40
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-29 19:57:03
 * @Description: 
 * 
 */
import type { GameFile } from '../types/emulator'
import { getFileExtension } from './fileHandler'

export async function loadGameFromUrl(url: string, filename: string): Promise<GameFile> {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`获取游戏文件失败: ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const extension = getFileExtension(filename)

    return {
      name: filename,
      extension,
      data: arrayBuffer,
      size: arrayBuffer.byteLength
    }
  } catch (error) {
    console.error('从URL加载游戏时出错:', error)
    throw error
  }
}

// Load the Game Boy game from root directory
export async function loadDefaultGame(): Promise<GameFile | null> {
  try {
    const gameUrl = '/f2c137516b31f1551fa0b14505dc8290e2fb5040f92abd4cadd4b92c9467b5d6.gb.zip'

    const response = await fetch(gameUrl)
    if (!response.ok) {
      console.warn('未找到默认游戏文件')
      return null
    }

    // Extract the .gb file from the ZIP
    const JSZip = (await import('jszip')).default
    const zipData = await response.arrayBuffer()
    const zip = new JSZip()
    const zipContent = await zip.loadAsync(zipData)

    // Find the first .gb file in the ZIP
    console.log('ZIP文件内容:', Object.keys(zipContent.files));

    for (const [filename, zipEntry] of Object.entries(zipContent.files)) {
      console.log('检查文件:', filename, '是否为目录:', zipEntry.dir);
      if (!zipEntry.dir && filename.toLowerCase().endsWith('.gb')) {
        const gameData = await zipEntry.async('arraybuffer')
        console.log('找到GB文件:', filename, '大小:', gameData.byteLength);
        return {
          name: filename,
          extension: 'gb',
          data: gameData,
          size: gameData.byteLength
        }
      }
    }

    throw new Error('ZIP文件中未找到.gb游戏文件')
  } catch (error) {
    console.warn('无法加载默认游戏:', error)
    return null
  }
} 